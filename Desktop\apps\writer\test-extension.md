# Extension Debugging Steps

## Current Issue: Commands Not Found

The extension is compiled correctly but commands are not being registered. This suggests the extension isn't activating.

## Immediate Solutions:

### Solution 1: Use Development Mode (Guaranteed to Work)

1. **Open the project in VS Code**:
   ```
   File > Open Folder > Select: Desktop/apps/writer
   ```

2. **Launch Extension Development Host**:
   ```
   Press F5 (or Run > Start Debugging)
   ```

3. **Test in the new window**:
   - A new VS Code window will open with the extension loaded
   - All commands should work immediately
   - The sidebar panel should appear

### Solution 2: Force Extension Activation

1. **Check if extension is installed**:
   ```
   Ctrl+Shift+X > Search "Essay Writer Agent"
   ```

2. **If installed, check activation**:
   ```
   Help > Toggle Developer Tools > Console tab
   Look for "Essay Writer Agent extension is now active!"
   ```

3. **If not activated, try**:
   ```
   Ctrl+Shift+P > "Developer: Reload Window"
   ```

### Solution 3: Manual Installation Check

1. **Uninstall completely**:
   ```
   Ctrl+Shift+X > Find extension > Uninstall
   Restart VS Code
   ```

2. **Reinstall from VSIX**:
   ```
   Ctrl+Shift+X > "..." menu > "Install from VSIX..."
   Select: essay-writer-agent-1.0.0.vsix
   ```

3. **Force reload**:
   ```
   Ctrl+Shift+P > "Developer: Reload Window"
   ```

## Debug Information:

### Expected Console Output:
When the extension loads, you should see:
```
Essay Writer Agent extension is now active!
```

### Expected Commands:
- essayWriterAgent.openPanel
- essayWriterAgent.brainstormTopics  
- essayWriterAgent.writeEssay
- essayWriterAgent.setSystemPrompt
- essayWriterAgent.configureAPI

### Expected Sidebar:
- Should appear in Explorer panel (Ctrl+Shift+E)
- Named "Essay Writer Agent"

## If Still Not Working:

The most reliable method is **Development Mode**:
1. Open project folder in VS Code
2. Press F5
3. Test in the Extension Development Host window

This bypasses any installation issues and loads the extension directly from source.
