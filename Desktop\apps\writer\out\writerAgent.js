"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WriterAgent = void 0;
const vscode = __importStar(require("vscode"));
const apiManager_1 = require("./apiManager");
const systemPromptManager_1 = require("./systemPromptManager");
const topicManager_1 = require("./topicManager");
class WriterAgent {
    constructor(context) {
        this.essays = [];
        this.context = context;
        this.apiManager = apiManager_1.APIManager.getInstance();
        this.systemPromptManager = systemPromptManager_1.SystemPromptManager.getInstance();
        this.topicManager = topicManager_1.TopicManager.getInstance();
        this.loadEssays();
    }
    static getInstance(context) {
        if (!WriterAgent.instance && context) {
            WriterAgent.instance = new WriterAgent(context);
        }
        return WriterAgent.instance;
    }
    async loadEssays() {
        const savedEssays = this.context.workspaceState.get('essays', []);
        this.essays = savedEssays.map(essay => ({
            ...essay,
            created: new Date(essay.created),
            modified: new Date(essay.modified)
        }));
    }
    async saveEssays() {
        await this.context.workspaceState.update('essays', this.essays);
    }
    async writeEssay(request) {
        const topic = this.topicManager.getTopic(request.topicId);
        if (!topic) {
            throw new Error(`Topic not found: ${request.topicId}`);
        }
        if (!topic.approved) {
            throw new Error('Topic must be approved before writing essay');
        }
        const essayPrompt = this.buildEssayPrompt(topic, request.config);
        const systemPrompt = this.systemPromptManager.buildFinalPrompt(this.buildEssaySystemPrompt(request.config), request.systemPrompt);
        try {
            vscode.window.showInformationMessage('Generating essay... This may take a moment.');
            const response = await this.apiManager.generateText(essayPrompt, systemPrompt, {
                maxTokens: this.calculateMaxTokens(request.config.wordCount),
                temperature: 0.7
            });
            const essay = {
                id: this.generateEssayId(),
                topicId: request.topicId,
                title: topic.title,
                content: response.content,
                config: request.config,
                created: new Date(),
                modified: new Date()
            };
            this.essays.push(essay);
            await this.saveEssays();
            vscode.window.showInformationMessage('Essay generated successfully!');
            return essay;
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to generate essay: ${error}`);
            throw error;
        }
    }
    buildEssayPrompt(topic, config) {
        let prompt = `Write a comprehensive essay on the topic: "${topic.title}"`;
        if (topic.description) {
            prompt += `\n\nTopic description: ${topic.description}`;
        }
        prompt += `\n\nEssay Requirements:
- Target word count: approximately ${config.wordCount} words
- Writing tone: ${config.tone}
- Include citations: ${config.includeCitations ? 'Yes' : 'No'}`;
        if (config.sections && config.sections.length > 0) {
            prompt += `\n- Essay structure: ${config.sections.map(s => s.type).join(', ')}`;
        }
        prompt += `\n\nInstructions:
- Write a well-structured, coherent essay that thoroughly explores the topic
- Use clear topic sentences and smooth transitions between paragraphs
- Support arguments with relevant examples and evidence`;
        if (config.includeCitations) {
            prompt += `\n- Include appropriate citations in academic format`;
        }
        prompt += `\n- Maintain the specified tone throughout the essay
- Ensure the essay meets the target word count
- Create an engaging introduction and a strong conclusion

Please write the complete essay now:`;
        return prompt;
    }
    buildEssaySystemPrompt(config) {
        let systemPrompt = 'You are an expert essay writer with advanced knowledge across multiple disciplines.';
        switch (config.tone) {
            case 'academic':
                systemPrompt += ' Write in a formal, scholarly tone with sophisticated vocabulary and complex sentence structures.';
                break;
            case 'casual':
                systemPrompt += ' Write in a conversational, accessible tone that engages general readers.';
                break;
            case 'critical':
                systemPrompt += ' Write with a critical, analytical perspective that examines multiple viewpoints.';
                break;
            case 'persuasive':
                systemPrompt += ' Write persuasively with strong arguments and compelling evidence.';
                break;
            case 'poetic':
                systemPrompt += ' Write with literary flair, using vivid imagery and elegant prose.';
                break;
            case 'analytical':
                systemPrompt += ' Write with deep analysis, breaking down complex concepts systematically.';
                break;
        }
        if (config.includeCitations) {
            systemPrompt += ' Include proper citations and references to support your arguments.';
        }
        return systemPrompt;
    }
    calculateMaxTokens(wordCount) {
        // Rough estimate: 1 token ≈ 0.75 words
        // Add buffer for formatting and structure
        return Math.ceil((wordCount / 0.75) * 1.3);
    }
    generateEssayId() {
        return `essay-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    getAllEssays() {
        return [...this.essays];
    }
    getEssay(id) {
        return this.essays.find(essay => essay.id === id);
    }
    getEssaysByTopic(topicId) {
        return this.essays.filter(essay => essay.topicId === topicId);
    }
    async deleteEssay(id) {
        const index = this.essays.findIndex(e => e.id === id);
        if (index === -1) {
            return false;
        }
        this.essays.splice(index, 1);
        await this.saveEssays();
        return true;
    }
    async updateEssayContent(id, content) {
        const essay = this.essays.find(e => e.id === id);
        if (!essay) {
            return false;
        }
        essay.content = content;
        essay.modified = new Date();
        await this.saveEssays();
        return true;
    }
    async exportEssay(id, format = 'markdown') {
        const essay = this.getEssay(id);
        if (!essay) {
            throw new Error('Essay not found');
        }
        let content = '';
        if (format === 'markdown') {
            content = `# ${essay.title}\n\n`;
            content += `*Generated on: ${essay.created.toLocaleDateString()}*\n`;
            content += `*Word count: ~${essay.config.wordCount} words*\n`;
            content += `*Tone: ${essay.config.tone}*\n\n`;
            content += essay.content;
        }
        else {
            content = `${essay.title}\n`;
            content += `Generated on: ${essay.created.toLocaleDateString()}\n`;
            content += `Word count: ~${essay.config.wordCount} words\n`;
            content += `Tone: ${essay.config.tone}\n\n`;
            content += essay.content;
        }
        return content;
    }
    async saveEssayToFile(id, format = 'markdown') {
        const essay = this.getEssay(id);
        if (!essay) {
            throw new Error('Essay not found');
        }
        const content = await this.exportEssay(id, format);
        const extension = format === 'markdown' ? 'md' : 'txt';
        const fileName = `${essay.title.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`;
        const uri = await vscode.window.showSaveDialog({
            defaultUri: vscode.Uri.file(fileName),
            filters: {
                [format === 'markdown' ? 'Markdown' : 'Text']: [extension]
            }
        });
        if (uri) {
            await vscode.workspace.fs.writeFile(uri, Buffer.from(content, 'utf8'));
            vscode.window.showInformationMessage(`Essay saved to ${uri.fsPath}`);
        }
    }
    getEssayStats() {
        const total = this.essays.length;
        const totalWords = this.essays.reduce((sum, essay) => sum + essay.config.wordCount, 0);
        const averageWords = total > 0 ? Math.round(totalWords / total) : 0;
        return { total, totalWords, averageWords };
    }
}
exports.WriterAgent = WriterAgent;
//# sourceMappingURL=writerAgent.js.map