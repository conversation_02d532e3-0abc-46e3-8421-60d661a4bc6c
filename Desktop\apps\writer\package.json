{"name": "essay-writer-agent", "displayName": "Essay Writer Agent", "description": "AI-powered writing assistant for essays and academic content with structured workflow for brainstorming, editing, and generating full essay passages.", "version": "1.0.1", "publisher": "essay-writer-agent", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Education"], "keywords": ["ai", "essay", "writing", "academic", "assistant", "openai", "gemini"], "activationEvents": ["*", "onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "essayWriterAgent.openPanel", "title": "Open Essay Writer Agent", "category": "Essay Writer"}, {"command": "essayWriterAgent.brainstormTopics", "title": "Brainstorm Essay Topics", "category": "Essay Writer"}, {"command": "essayWriterAgent.writeEssay", "title": "Write Essay", "category": "Essay Writer"}, {"command": "essayWriterAgent.editContent", "title": "Edit Content", "category": "Essay Writer"}, {"command": "essayWriterAgent.setSystemPrompt", "title": "Set System Prompt", "category": "Essay Writer"}], "views": {"explorer": [{"id": "essayWriterAgent.sidebarView", "name": "Essay Writer Agent", "when": "true", "contextualTitle": "Essay Writer Agent"}]}, "viewsWelcome": [{"view": "essayWriterAgent.sidebarView", "contents": "Welcome to Essay Writer Agent!\n[Open Essay Writer Panel](command:essayWriterAgent.openPanel)\n[Brainstorm Topics](command:essayWriterAgent.brainstormTopics)"}], "configuration": {"title": "Essay Writer Agent", "properties": {"essayWriterAgent.apiProvider": {"type": "string", "default": "openai", "enum": ["openai", "gemini", "deepseek"], "description": "Primary AI API provider"}, "essayWriterAgent.openaiApiKey": {"type": "string", "default": "", "description": "OpenAI API Key"}, "essayWriterAgent.geminiApiKey": {"type": "string", "default": "", "description": "Google Gemini API Key"}, "essayWriterAgent.deepseekApiKey": {"type": "string", "default": "", "description": "DeepSeek API Key"}, "essayWriterAgent.globalSystemPrompt": {"type": "string", "default": "", "description": "Global system prompt applied to all AI requests"}, "essayWriterAgent.enableGlobalSystemPrompt": {"type": "boolean", "default": false, "description": "Enable global system prompt"}, "essayWriterAgent.defaultWordCount": {"type": "number", "default": 800, "description": "Default word count for essays"}, "essayWriterAgent.defaultTone": {"type": "string", "default": "academic", "enum": ["academic", "casual", "critical", "poetic", "persuasive", "analytical"], "description": "Default writing tone"}}}}, "keybindings": [{"command": "essayWriterAgent.openPanel", "key": "ctrl+l", "when": "!terminalFocus"}], "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@types/mocha": "^10.0.0", "@types/glob": "^8.0.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "mocha": "^10.0.0", "glob": "^8.0.0", "vsce": "^2.15.0"}, "dependencies": {"axios": "^1.6.0"}}