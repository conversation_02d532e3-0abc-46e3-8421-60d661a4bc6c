{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAAwC;AACxC,iDAA8C;AAC9C,+CAA4C;AAC5C,+DAA4D;AAC5D,+CAA4C;AAC5C,qCAAyC;AACzC,6CAA0C;AAE1C,SAAgB,QAAQ,CAAC,OAAgC;IACvD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uDAAuD,CAAC,CAAC;IAE9F,sBAAsB;IACtB,MAAM,aAAa,GAAG,sBAAa,CAAC,WAAW,EAAE,CAAC;IAClD,MAAM,UAAU,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC;IAC5C,MAAM,mBAAmB,GAAG,yCAAmB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACrE,MAAM,YAAY,GAAG,2BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACvD,MAAM,WAAW,GAAG,yBAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACrD,MAAM,WAAW,GAAG,yBAAW,CAAC,WAAW,EAAE,CAAC;IAC9C,MAAM,SAAS,GAAG,IAAI,qBAAS,CAAC,OAAO,CAAC,CAAC;IAEzC,4BAA4B;IAC5B,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,qBAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,CACzE,CAAC;IAEF,oBAAoB;IACpB,gBAAgB,CAAC,OAAO,EAAE;QACxB,YAAY;QACZ,WAAW;QACX,mBAAmB;QACnB,WAAW;QACX,aAAa;QACb,UAAU;KACX,CAAC,CAAC;IAEH,iCAAiC;IACjC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IAE1C,2CAA2C;IAC3C,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC,CAAC;IAChF,IAAI,WAAW,EAAE;QACf,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC5B,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;KACjE;IAED,0BAA0B;IAC1B,qBAAqB,CAAC,aAAa,CAAC,CAAC;AACvC,CAAC;AAxCD,4BAwCC;AAED,SAAS,gBAAgB,CACvB,OAAgC,EAChC,QAOC;IAED,0BAA0B;IAC1B,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CACtD,4BAA4B,EAC5B,GAAG,EAAE;QACH,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;QAC1D,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;IACvE,CAAC,CACF,CAAC;IAEF,4BAA4B;IAC5B,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CACvD,mCAAmC,EACnC,KAAK,IAAI,EAAE;QACT,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC9C,MAAM,EAAE,0EAA0E;YAClF,WAAW,EAAE,iEAAiE;SAC/E,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAChD,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EACvB,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAChD,CAAC;QAEF,IAAI,QAAQ,EAAE;YACZ,IAAI;gBACF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC;oBAC1D,MAAM,EAAE,MAAM,IAAI,EAAE;oBACpB,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;iBAC1B,CAAC,CAAC;gBACH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,aAAa,MAAM,CAAC,MAAM,UAAU,CAAC,CAAC;aAC5E;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;aACzE;SACF;IACH,CAAC,CACF,CAAC;IAEF,sBAAsB;IACtB,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CACvD,6BAA6B,EAC7B,KAAK,IAAI,EAAE;QACT,MAAM,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACjE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,2EAA2E,CAAC,CAAC;YAC9G,OAAO;SACR;QAED,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC9C,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,KAAK;SACN,CAAC,CAAC,CAAC;QAEJ,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE;YACjE,WAAW,EAAE,+BAA+B;SAC7C,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE;YAChB,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;YAE9D,iCAAiC;YACjC,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CACjD,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EACtC,EAAE,WAAW,EAAE,mBAAmB,EAAE,CACrC,CAAC;YAEF,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAC5C,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,CAAC,EACxE,EAAE,WAAW,EAAE,qBAAqB,EAAE,CACvC,CAAC;YAEF,IAAI,SAAS,IAAI,IAAI,EAAE;gBACrB,MAAM,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,GAAG,IAAW,CAAC;gBAE1B,IAAI;oBACF,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,UAAU,CAAC;wBAClD,OAAO,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;wBAC9B,MAAM;qBACP,CAAC,CAAC;oBAEH,6BAA6B;oBAC7B,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;wBAClD,OAAO,EAAE,KAAK,CAAC,OAAO;wBACtB,QAAQ,EAAE,UAAU;qBACrB,CAAC,CAAC;oBACH,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;iBAC3C;gBAAC,OAAO,KAAK,EAAE;oBACd,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;iBACnE;aACF;SACF;IACH,CAAC,CACF,CAAC;IAEF,4BAA4B;IAC5B,MAAM,sBAAsB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAC5D,kCAAkC,EAClC,KAAK,IAAI,EAAE;QACT,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;QACrE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YACjD,MAAM,EAAE,4BAA4B;YACpC,KAAK,EAAE,aAAa;YACpB,WAAW,EAAE,8CAA8C;SAC5D,CAAC,CAAC;QAEH,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,MAAM,QAAQ,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAC1E,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wBAAwB,CAAC,CAAC;SAChE;IACH,CAAC,CACF,CAAC;IAEF,gDAAgD;IAChD,2DAA2D;IAE3D,yBAAyB;IACzB,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CACzD,+BAA+B,EAC/B,KAAK,IAAI,EAAE;QACT,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAChD,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,CAAC,EAChC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CACvC,CAAC;QAEF,IAAI,QAAQ,EAAE;YACZ,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC9C,MAAM,EAAE,SAAS,QAAQ,CAAC,WAAW,EAAE,UAAU;gBACjD,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;YAEH,IAAI,MAAM,EAAE;gBACV,MAAM,QAAQ,CAAC,aAAa,CAAC,cAAc,CAAC,QAAe,CAAC,CAAC;gBAE7D,QAAQ,QAAQ,EAAE;oBAChB,KAAK,QAAQ;wBACX,MAAM,QAAQ,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;wBACrD,MAAM;oBACR,KAAK,QAAQ;wBACX,MAAM,QAAQ,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;wBACrD,MAAM;oBACR,KAAK,UAAU;wBACb,MAAM,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;wBACvD,MAAM;iBACT;gBAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,QAAQ,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;aACnF;SACF;IACH,CAAC,CACF,CAAC;IAEF,oCAAoC;IACpC,OAAO,CAAC,aAAa,CAAC,IAAI,CACxB,gBAAgB,EAChB,iBAAiB,EACjB,iBAAiB,EACjB,sBAAsB,EACtB,mBAAmB,CACpB,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;IACzE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,0CAA0C,CAAC,CAAC;AACnF,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAgC;IAC1D,MAAM,OAAO,GAAG,wEAAwE,CAAC;IACzF,MAAM,eAAe,GAAG,eAAe,CAAC;IACxC,MAAM,eAAe,GAAG,YAAY,CAAC;IAErC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,eAAe,EAAE,eAAe,CAAC;SAC5E,IAAI,CAAC,SAAS,CAAC,EAAE;QAChB,IAAI,SAAS,KAAK,eAAe,EAAE;YACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;SACjE;aAAM,IAAI,SAAS,KAAK,eAAe,EAAE;YACxC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,4BAA4B,CAAC,CAAC;SAC9D;IACH,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,qBAAqB,CAAC,aAA4B;IACzD,MAAM,UAAU,GAAG,aAAa,CAAC,wBAAwB,EAAE,CAAC;IAC5D,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;QACvB,MAAM,OAAO,GAAG,+EAA+E,CAAC;QAChG,MAAM,eAAe,GAAG,eAAe,CAAC;QAExC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,eAAe,CAAC;aACvD,IAAI,CAAC,SAAS,CAAC,EAAE;YAChB,IAAI,SAAS,KAAK,eAAe,EAAE;gBACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;aACjE;QACH,CAAC,CAAC,CAAC;KACN;AACH,CAAC;AAED,SAAgB,UAAU;IACxB,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;AAClE,CAAC;AAFD,gCAEC"}