import * as vscode from 'vscode';
import { UIManager } from './uiManager';
import { TopicManager } from './topicManager';
import { WriterAgent } from './writerAgent';
import { SystemPromptManager } from './systemPromptManager';
import { EditorTools } from './editorTools';
import { ConfigManager } from './config';
import { APIManager } from './apiManager';

export function activate(context: vscode.ExtensionContext) {
  console.log('Essay Writer Agent extension is now active!');
  vscode.window.showInformationMessage('Essay Writer Agent: Extension activated successfully!');

  // Initialize managers
  const configManager = ConfigManager.getInstance();
  const apiManager = APIManager.getInstance();
  const systemPromptManager = SystemPromptManager.getInstance(context);
  const topicManager = TopicManager.getInstance(context);
  const writerAgent = WriterAgent.getInstance(context);
  const editorTools = EditorTools.getInstance();
  const uiManager = new UIManager(context);

  // Register webview provider
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(UIManager.viewType, uiManager)
  );

  // Register commands
  registerCommands(context, {
    topicManager,
    writerAgent,
    systemPromptManager,
    editorTools,
    configManager,
    apiManager
  });

  // Register editor tools commands
  editorTools.registerEditCommands(context);

  // Show welcome message on first activation
  const isFirstTime = context.globalState.get('essayWriterAgent.firstTime', true);
  if (isFirstTime) {
    showWelcomeMessage(context);
    context.globalState.update('essayWriterAgent.firstTime', false);
  }

  // Check API configuration
  checkAPIConfiguration(configManager);
}

function registerCommands(
  context: vscode.ExtensionContext,
  managers: {
    topicManager: TopicManager;
    writerAgent: WriterAgent;
    systemPromptManager: SystemPromptManager;
    editorTools: EditorTools;
    configManager: ConfigManager;
    apiManager: APIManager;
  }
) {
  // Open Essay Writer Panel
  const openPanelCommand = vscode.commands.registerCommand(
    'essayWriterAgent.openPanel',
    () => {
      vscode.commands.executeCommand('workbench.view.explorer');
      vscode.commands.executeCommand('essayWriterAgent.sidebarView.focus');
    }
  );

  // Brainstorm Topics Command
  const brainstormCommand = vscode.commands.registerCommand(
    'essayWriterAgent.brainstormTopics',
    async () => {
      const prompt = await vscode.window.showInputBox({
        prompt: 'Enter a topic area for brainstorming (or leave blank for general topics)',
        placeHolder: 'e.g., "climate change", "artificial intelligence", "philosophy"'
      });

      const countStr = await vscode.window.showQuickPick(
        ['5', '10', '15', '20'],
        { placeHolder: 'How many topics to generate?' }
      );

      if (countStr) {
        try {
          const topics = await managers.topicManager.brainstormTopics({
            prompt: prompt || '',
            count: parseInt(countStr)
          });
          vscode.window.showInformationMessage(`Generated ${topics.length} topics!`);
        } catch (error) {
          vscode.window.showErrorMessage(`Failed to brainstorm topics: ${error}`);
        }
      }
    }
  );

  // Write Essay Command
  const writeEssayCommand = vscode.commands.registerCommand(
    'essayWriterAgent.writeEssay',
    async () => {
      const approvedTopics = managers.topicManager.getApprovedTopics();
      if (approvedTopics.length === 0) {
        vscode.window.showWarningMessage('No approved topics available. Please brainstorm and approve topics first.');
        return;
      }

      const topicItems = approvedTopics.map(topic => ({
        label: topic.title,
        description: topic.description,
        topic
      }));

      const selectedItem = await vscode.window.showQuickPick(topicItems, {
        placeHolder: 'Select a topic to write about'
      });

      if (selectedItem) {
        const config = managers.configManager.getDefaultEssayConfig();
        
        // Allow user to customize config
        const wordCount = await vscode.window.showQuickPick(
          ['500', '800', '1200', '1500', '2000'],
          { placeHolder: 'Select word count' }
        );

        const tone = await vscode.window.showQuickPick(
          ['academic', 'casual', 'critical', 'persuasive', 'analytical', 'poetic'],
          { placeHolder: 'Select writing tone' }
        );

        if (wordCount && tone) {
          config.wordCount = parseInt(wordCount);
          config.tone = tone as any;

          try {
            const essay = await managers.writerAgent.writeEssay({
              topicId: selectedItem.topic.id,
              config
            });

            // Open essay in new document
            const doc = await vscode.workspace.openTextDocument({
              content: essay.content,
              language: 'markdown'
            });
            await vscode.window.showTextDocument(doc);
          } catch (error) {
            vscode.window.showErrorMessage(`Failed to write essay: ${error}`);
          }
        }
      }
    }
  );

  // Set System Prompt Command
  const setSystemPromptCommand = vscode.commands.registerCommand(
    'essayWriterAgent.setSystemPrompt',
    async () => {
      const currentPrompt = managers.configManager.getGlobalSystemPrompt();
      const newPrompt = await vscode.window.showInputBox({
        prompt: 'Enter global system prompt',
        value: currentPrompt,
        placeHolder: 'System prompt to apply to all AI requests...'
      });

      if (newPrompt !== undefined) {
        await managers.systemPromptManager.setGlobalSystemPrompt(newPrompt, true);
        vscode.window.showInformationMessage('System prompt updated!');
      }
    }
  );

  // Edit Content Command (handled by EditorTools)
  // Already registered in editorTools.registerEditCommands()

  // Configuration Commands
  const configureAPICommand = vscode.commands.registerCommand(
    'essayWriterAgent.configureAPI',
    async () => {
      const provider = await vscode.window.showQuickPick(
        ['openai', 'gemini', 'deepseek'],
        { placeHolder: 'Select API provider' }
      );

      if (provider) {
        const apiKey = await vscode.window.showInputBox({
          prompt: `Enter ${provider.toUpperCase()} API key`,
          password: true
        });

        if (apiKey) {
          await managers.configManager.setAPIProvider(provider as any);
          
          switch (provider) {
            case 'openai':
              await managers.configManager.setOpenAIApiKey(apiKey);
              break;
            case 'gemini':
              await managers.configManager.setGeminiApiKey(apiKey);
              break;
            case 'deepseek':
              await managers.configManager.setDeepSeekApiKey(apiKey);
              break;
          }

          vscode.window.showInformationMessage(`${provider.toUpperCase()} API configured!`);
        }
      }
    }
  );

  // Add all commands to subscriptions
  context.subscriptions.push(
    openPanelCommand,
    brainstormCommand,
    writeEssayCommand,
    setSystemPromptCommand,
    configureAPICommand
  );

  console.log('Essay Writer Agent: All commands registered successfully!');
  vscode.window.showInformationMessage('Essay Writer Agent: Commands registered!');
}

function showWelcomeMessage(context: vscode.ExtensionContext) {
  const message = 'Welcome to Essay Writer Agent! Configure your API keys to get started.';
  const configureAction = 'Configure API';
  const openPanelAction = 'Open Panel';

  vscode.window.showInformationMessage(message, configureAction, openPanelAction)
    .then(selection => {
      if (selection === configureAction) {
        vscode.commands.executeCommand('essayWriterAgent.configureAPI');
      } else if (selection === openPanelAction) {
        vscode.commands.executeCommand('essayWriterAgent.openPanel');
      }
    });
}

function checkAPIConfiguration(configManager: ConfigManager) {
  const validation = configManager.validateAPIConfiguration();
  if (!validation.isValid) {
    const message = 'Essay Writer Agent: API configuration incomplete. Some features may not work.';
    const configureAction = 'Configure Now';
    
    vscode.window.showWarningMessage(message, configureAction)
      .then(selection => {
        if (selection === configureAction) {
          vscode.commands.executeCommand('essayWriterAgent.configureAPI');
        }
      });
  }
}

export function deactivate() {
  console.log('Essay Writer Agent extension is now deactivated.');
}
