{"name": "@types/minimatch", "version": "5.1.2", "description": "TypeScript definitions for minimatch", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/minimatch", "license": "MIT", "contributors": [{"name": "vvakame", "url": "https://github.com/vvakame", "githubUsername": "vvakame"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/shantmarouti", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/minimatch"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "266f2226f04264f59fb2aeb3afc253d311ddd99b4ae8534d2e27f8a1379203e4", "typeScriptVersion": "4.1"}